# 导入操作系统相关模块
import os

# 导入LazyLLM主模块
import lazyllm

# 导入RAG文档处理模块
from lazyllm.tools.rag import Document

# 导入句子分割器
from lazyllm import SentenceSplitter

# 导入流水线、并行处理、检索器、重排序器和绑定功能
from lazyllm import pipeline, parallel, Retriever, Reranker, bind


# 先测试LLM是否可以单独工作
print("=== 测试LLM模块 ===")
llm_default = lazyllm.OnlineChatModule(
    source="openai",
    model="ep-20250822223253-s98w6",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d",
    base_url="https://ark.cn-beijing.volces.com/api/v3/",  # 注意：末尾添加了斜杠
    stream=False,
)

# 测试LLM是否可以单独工作
try:
    test_result = llm_default("你好，请简单介绍一下自己")
    print(f"✓ LLM模块测试成功: {test_result}")
except Exception as e:
    print(f"✗ LLM模块测试失败: {e}")
    print("请检查API配置是否正确")
    exit(1)
print("\n=== 测试嵌入模块 ===")
embedding_default = lazyllm.OnlineEmbeddingModule(
    source="glm",
    embed_model_name="embedding-3",
    api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
    embed_url="https://open.bigmodel.cn/api/paas/v4/embeddings/",  # 注意：末尾添加了斜杠
)

# 测试嵌入模型是否可以工作
try:
    test_embedding = embedding_default("测试文本")
    print(f"✓ 嵌入模块测试成功，向量维度: {len(test_embedding) if isinstance(test_embedding, list) else 'unknown'}")
except Exception as e:
    print(f"✗ 嵌入模块测试失败: {e}")
    print("请检查GLM API配置是否正确")
Reranker_default = lazyllm.OnlineEmbeddingModule(
    type="rerank",
    source="glm",
    embed_model_name="rerank",
    api_key="e37e81fae0e04aab9340b25a2f351e42.c34b4fosyCYIJKhb",
    embed_url="https://open.bigmodel.cn/api/paas/v4/rerank",  # 修正为重排序API端点
)
# 定义AI问答助手的提示词模板
# 指导AI扮演问答助手角色，基于给定上下文和问题提供答案
prompt = (
    "You will play the role of an AI Q&A assistant and complete a dialogue task. "
    "In this task, you need to provide your answer based on the given context and question."
)

# 创建文档对象，使用默认的文档读取器
# dataset_path: 指定数据集路径为当前目录下的rag_data文件夹
# embed: 使用GLM的在线嵌入模型embedding-2进行文档向量化
# manager: 设置为False，不使用文档管理器
documents = Document(
    dataset_path=os.path.join(os.getcwd(), "rag_data"),
    embed=embedding_default,
    manager=False,
)

# 创建文档节点组，用于句子级别的文档分割
# name: 节点组名称为"sentences"
# transform: 使用SentenceSplitter进行文本分割
# chunk_size: 每个文档块的大小为1024个字符
# chunk_overlap: 文档块之间的重叠为100个字符，确保上下文连续性
documents.create_node_group(
    name="sentences", transform=SentenceSplitter, chunk_size=1024, chunk_overlap=100
)

# 构建RAG流水线
with pipeline() as ppl:
    # 并行检索阶段：使用两个不同的检索器
    with parallel().sum as ppl.prl:
        # 检索器1：基于句子级别的余弦相似度检索
        # group_name: 使用"sentences"节点组
        # similarity: 使用余弦相似度计算
        # topk: 返回前3个最相关的文档
        ppl.prl.retriever1 = Retriever(
            documents, group_name="sentences", similarity="cosine", topk=3
        )

        # 检索器2：基于粗粒度块的BM25中文检索
        # "CoarseChunk": 使用粗粒度文档块
        # "bm25_chinese": 使用BM25中文算法
        # 0.003: BM25算法的阈值参数
        # topk: 返回前3个最相关的文档
        ppl.prl.retriever2 = Retriever(
            documents, "CoarseChunk", "bm25_chinese", 0.003, topk=3
        )

    # 重排序阶段：对检索结果进行重新排序
    # ModuleReranker: 使用模块化重排序器
    # model: 使用已配置好API密钥的GLM重排序模型
    # topk: 最终返回1个最相关的文档
    # output_format: 输出格式为内容
    # join: 将多个结果合并
    # bind: 绑定查询输入
    ppl.reranker = Reranker(
        "ModuleReranker",
        model=Reranker_default,
        topk=1,
        output_format="content",
        join=True,
    ) | bind(query=ppl.input)

    # 格式化阶段：将检索到的节点和查询格式化为字典
    # lambda函数：接收nodes和query参数，返回包含context_str和query的字典
    # bind: 绑定查询输入
    def debug_formatter(nodes, query):
        print(f"\n=== 格式化阶段调试信息 ===")
        print(f"查询: {query}")
        print(f"检索到的节点数量: {len(nodes) if isinstance(nodes, (list, tuple)) else 'unknown'}")
        print(f"节点内容预览: {str(nodes)[:200]}...")
        result = dict(context_str=nodes, query=query)
        print(f"格式化结果: {result}")
        return result

    ppl.formatter = debug_formatter | bind(query=ppl.input)

    # 大语言模型阶段：使用GLM-4模型生成最终答案
    # source: 使用GLM作为模型源
    # model: 指定使用glm-4模型
    # stream: 设置为False，不使用流式输出
    # prompt: 使用ChatPrompter配置提示词，extra_keys指定额外的上下文键
    ppl.llm = llm_default.prompt(
        lazyllm.ChatPrompter(prompt, extra_keys=["context_str"])
    )

# 连续问答功能
def continuous_qa():
    """连续问答函数"""
    print("\n" + "="*60)
    print("🤖 RAG智能问答系统已启动")
    print("📚 已加载文档库，可以开始提问了")
    print("💡 输入 'quit'、'exit' 或 'q' 退出系统")
    print("="*60)

    while True:
        try:
            # 获取用户输入
            print("\n" + "-"*40)
            question = input("🙋 请输入您的问题: ").strip()

            # 检查退出命令
            if question.lower() in ['quit', 'exit', 'q', '退出']:
                print("\n👋 感谢使用RAG智能问答系统，再见！")
                break

            # 检查空输入
            if not question:
                print("❌ 请输入有效的问题")
                continue

            print(f"\n🔍 正在搜索相关信息...")

            # 执行查询
            result = ppl(question)

            print(f"\n✅ 查询完成！")
            print("-"*40)
            print("📝 回答:")
            print(result)
            print("-"*40)

        except KeyboardInterrupt:
            print("\n\n👋 检测到中断信号，正在退出...")
            break
        except Exception as e:
            print(f"\n❌ 查询失败，错误信息：{e}")
            print("\n🔧 可能的原因：")
            print("1. 网络连接问题 - 无法访问API服务")
            print("2. API密钥无效或已过期")
            print("3. 模型名称不正确")
            print("4. 服务端暂时不可用")
            print("\n💡 建议解决方案：")
            print("1. 检查网络连接")
            print("2. 验证API密钥是否有效")
            print("3. 确认模型名称是否正确")
            print("4. 稍后重试")

            # 询问是否继续
            continue_choice = input("\n是否继续尝试？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是', '继续']:
                break

# 启动连续问答
if __name__ == "__main__":
    continuous_qa()
